package biz.zhizuo.ai.assistant.repository

import biz.zhizuo.ai.assistant.entity.*
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

/**
 * 领域 Repository
 */
@Repository
interface DomainRepository : JpaRepository<Domain, String> {

    /**
     * 查找活跃的领域，按优先级排序
     */
    fun findByIsActiveTrueOrderByPriorityOrderAsc(): List<Domain>

    /**
     * 根据名称查找领域
     */
    fun findByNameAndIsActiveTrue(name: String): Domain?

    /**
     * 查找所有活跃的领域（用于向量匹配）
     */
    @Query("SELECT d FROM Domain d WHERE d.isActive = true AND d.embeddingVector IS NOT NULL")
    fun findAllActiveWithEmbedding(): List<Domain>
}

/**
 * 执行计划 Repository
 */
@Repository
interface ExecutionPlanRepository : JpaRepository<ExecutionPlan, String> {

    /**
     * 查找活跃的执行计划，按优先级排序
     */
    fun findByIsActiveTrueOrderByPriorityOrderAsc(): List<ExecutionPlan>

    /**
     * 根据领域查找执行计划
     */
    fun findByDomainAndIsActiveTrueOrderByPriorityOrderAsc(domain: Domain): List<ExecutionPlan>

    /**
     * 根据领域ID查找执行计划
     */
    fun findByDomainIdAndIsActiveTrueOrderByPriorityOrderAsc(domainId: String): List<ExecutionPlan>

    /**
     * 查找领域下所有有嵌入向量的活跃执行计划
     */
    @Query("SELECT ep FROM ExecutionPlan ep WHERE ep.domain = :domain AND ep.isActive = true AND ep.embeddingVector IS NOT NULL")
    fun findActiveWithEmbeddingByDomain(@Param("domain") domain: Domain): List<ExecutionPlan>

    /**
     * 使用向量相似度搜索执行计划（需要数据库支持向量操作）
     */
    @Query(value = """
        SELECT * FROM execution_plans ep
        WHERE ep.domain_id = :domainId
        AND ep.is_active = true
        AND ep.embedding_vector IS NOT NULL
        ORDER BY ep.priority_order ASC
        LIMIT :limit
    """, nativeQuery = true)
    fun findSimilarPlansByDomain(
        @Param("domainId") domainId: String,
        @Param("limit") limit: Int
    ): List<ExecutionPlan>

    /**
     * 使用向量相似度搜索执行计划（简化版本，用于测试）
     */
    @Query("SELECT ep FROM ExecutionPlan ep WHERE ep.isActive = true AND ep.embeddingVector IS NOT NULL ORDER BY ep.priorityOrder ASC")
    fun findSimilarPlans(@Param("queryVector") queryVector: String, @Param("limit") limit: Int): List<ExecutionPlan>
}

/**
 * 执行计划步骤 Repository
 */
@Repository
interface ExecutionPlanStepRepository : JpaRepository<ExecutionPlanStep, String> {

    /**
     * 根据执行计划ID查找步骤，按顺序排序
     */
    fun findByExecutionPlanIdOrderByStepOrderAsc(executionPlanId: String): List<ExecutionPlanStep>

    /**
     * 根据执行计划查找步骤，按顺序排序
     */
    fun findByExecutionPlanOrderByStepOrderAsc(executionPlan: ExecutionPlan): List<ExecutionPlanStep>
}

/**
 * 消息执行上下文 Repository
 */
@Repository
interface MessageExecutionContextRepository : JpaRepository<MessageExecutionContext, String> {

    /**
     * 根据消息ID查找执行上下文
     */
    fun findByMessageId(messageId: String): MessageExecutionContext?

    /**
     * 根据消息查找执行上下文
     */
    fun findByMessage(message: ChatMessage): MessageExecutionContext?

    /**
     * 查找正在执行的上下文
     */
    fun findByStatusIn(statuses: List<ExecutionStatus>): List<MessageExecutionContext>

    /**
     * 根据用户ID查找执行上下文
     */
    @Query("SELECT mec FROM MessageExecutionContext mec WHERE mec.message.user.id = :userId")
    fun findByUserId(@Param("userId") userId: String): List<MessageExecutionContext>
}

/**
 * 步骤执行记录 Repository
 */
@Repository
interface StepExecutionRepository : JpaRepository<StepExecution, String> {

    /**
     * 根据执行上下文查找步骤执行记录
     */
    fun findByExecutionContextOrderByExecutionPlanStepStepOrderAsc(
        executionContext: MessageExecutionContext
    ): List<StepExecution>

    /**
     * 根据执行上下文ID查找步骤执行记录
     */
    fun findByExecutionContextIdOrderByExecutionPlanStepStepOrderAsc(
        executionContextId: String
    ): List<StepExecution>

    /**
     * 查找正在执行的步骤
     */
    fun findByStatusIn(statuses: List<StepStatus>): List<StepExecution>

    /**
     * 根据执行上下文和步骤顺序查找执行记录
     */
    fun findByExecutionContextAndExecutionPlanStepStepOrder(
        executionContext: MessageExecutionContext,
        stepOrder: Int
    ): StepExecution?
}
