package biz.zhizuo.ai.assistant.entity

import jakarta.persistence.*
import org.hibernate.annotations.UuidGenerator

/**
 * 聊天消息生成步骤实体
 */
@Entity
@Table(name = "chat_message_generation_steps")
data class ChatMessageGenerationStep(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @UuidGenerator(style = UuidGenerator.Style.TIME)
    val id: String? = null,

    @ManyToOne
    val message: ChatMessage,

    @Column(nullable = false)
    val step: String,

    @Column(columnDefinition = "TEXT")
    val description: String? = null,

    @Column(name = "step_order", nullable = false)
    val stepOrder: Int,

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    val status: StepStatus = StepStatus.PENDING,
)
