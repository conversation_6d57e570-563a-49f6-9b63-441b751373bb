package biz.zhizuo.ai.assistant.controller

import biz.zhizuo.ai.assistant.dto.AgentDto
import biz.zhizuo.ai.assistant.dto.SessionAgentDto
import biz.zhizuo.ai.assistant.dto.SubscriptionStatus
import biz.zhizuo.ai.assistant.dto.UserAgentSubscriptionDto
import biz.zhizuo.ai.assistant.entity.Agent
import biz.zhizuo.ai.assistant.entity.SessionAgent
import biz.zhizuo.ai.assistant.entity.User
import biz.zhizuo.ai.assistant.entity.UserAgentSubscription
import biz.zhizuo.ai.assistant.repository.AgentRepository
import biz.zhizuo.ai.assistant.service.AgentService
import biz.zhizuo.ai.assistant.service.ChatSessionService
import biz.zhizuo.ai.assistant.service.UserService
import org.springframework.http.ResponseEntity
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

/**
 * Agent管理控制器
 */
@RestController
@RequestMapping("/api/agents")
class AgentController(
    private val agentRepository: AgentRepository,
    private val agentService: AgentService,
    private val userService: UserService,
    private val chatSessionService: ChatSessionService,
) {

    /**
     * 获取所有活跃的Agent（包含用户订阅状态）
     */
    @GetMapping
    fun getAllActiveAgents(@AuthenticationPrincipal user: User): ResponseEntity<List<AgentDto>> {
        val agents = agentRepository.findByIsActiveTrueOrderByPriorityOrderAsc()
        val subscribedAgents = agentService.getUserSubscribedAgents(user).map { it.id }.toSet()

        val agentDtos = agents.map { agent ->
            agent.toDto(subscribedAgents.contains(agent.id))
        }
        return ResponseEntity.ok(agentDtos)
    }

    /**
     * 根据ID获取Agent
     */
    @GetMapping("/{id}")
    fun getAgentById(
        @PathVariable id: String,
        @AuthenticationPrincipal user: User,
    ): ResponseEntity<AgentDto> {
        val agent = agentRepository.findById(id)
            .orElseThrow { IllegalArgumentException("Agent不存在: $id") }
        val subscribedAgents = agentService.getUserSubscribedAgents(user).map { it.id }.toSet()

        return ResponseEntity.ok(agent.toDto(subscribedAgents.contains(agent.id)))
    }

    /**
     * 订阅Agent
     */
    @PostMapping("/{id}/subscribe")
    fun subscribeAgent(
        @PathVariable id: String,
        @AuthenticationPrincipal user: User,
    ): ResponseEntity<Map<String, Any>> {
        val agent = agentRepository.findById(id)
            .orElseThrow { IllegalArgumentException("Agent不存在: $id") }

        val subscription = agentService.subscribeAgent(user, agent)
        return ResponseEntity.ok(
            mapOf(
                "message" to "订阅成功",
                "subscription" to subscription.toDto()
            )
        )
    }

    /**
     * 取消订阅Agent
     */
    @PostMapping("/{id}/unsubscribe")
    fun unsubscribeAgent(
        @PathVariable id: String,
        @AuthenticationPrincipal user: User,
    ): ResponseEntity<Map<String, String>> {
        val agent = agentRepository.findById(id)
            .orElseThrow { IllegalArgumentException("Agent不存在: $id") }

        val success = agentService.unsubscribeAgent(user, agent)
        return if (success) {
            ResponseEntity.ok(mapOf("message" to "取消订阅成功"))
        } else {
            ResponseEntity.badRequest().body(mapOf("error" to "未找到有效订阅"))
        }
    }

    /**
     * 获取用户订阅的Agent列表
     */
    @GetMapping("/subscriptions")
    fun getUserSubscriptions(@AuthenticationPrincipal user: User): ResponseEntity<List<AgentDto>> {
        val subscribedAgents = agentService.getUserSubscribedAgents(user)
        val agentDtos = subscribedAgents.map { it.toDto(true) }
        return ResponseEntity.ok(agentDtos)
    }

    /**
     * 为会话添加Agent
     */
    @PostMapping("/sessions/{sessionId}/agents/{agentId}")
    fun addAgentToSession(
        @PathVariable sessionId: String,
        @PathVariable agentId: String,
        @AuthenticationPrincipal user: User,
    ): ResponseEntity<Map<String, Any>> {
        val session = chatSessionService.getSessionById(sessionId, user.id!!)
        val agent = agentRepository.findById(agentId)
            .orElseThrow { IllegalArgumentException("Agent不存在: $agentId") }

        // 检查用户是否订阅了该Agent
        val subscribedAgents = agentService.getUserSubscribedAgents(user)
        if (!subscribedAgents.any { it.id == agentId }) {
            return ResponseEntity.badRequest().body(
                mapOf(
                    "error" to "您尚未订阅该Agent"
                )
            )
        }

        val sessionAgent = agentService.addAgentToSession(session, agent)
        return ResponseEntity.ok(
            mapOf(
                "message" to "Agent已添加到会话",
                "sessionAgent" to sessionAgent.toDto()
            )
        )
    }

    /**
     * 从会话中移除Agent
     */
    @DeleteMapping("/sessions/{sessionId}/agents/{agentId}")
    fun removeAgentFromSession(
        @PathVariable sessionId: String,
        @PathVariable agentId: String,
        @AuthenticationPrincipal user: User,
    ): ResponseEntity<Map<String, String>> {
        val session = chatSessionService.getSessionById(sessionId, user.id!!)
        val agent = agentRepository.findById(agentId)
            .orElseThrow { IllegalArgumentException("Agent不存在: $agentId") }

        val success = agentService.removeAgentFromSession(session, agent)
        return if (success) {
            ResponseEntity.ok(mapOf("message" to "Agent已从会话中移除"))
        } else {
            ResponseEntity.badRequest().body(mapOf("error" to "Agent不在当前会话中"))
        }
    }

    /**
     * 获取会话中的Agent列表
     */
    @GetMapping("/sessions/{sessionId}/agents")
    fun getSessionAgents(
        @PathVariable sessionId: String,
        @AuthenticationPrincipal user: User,
    ): ResponseEntity<List<AgentDto>> {
        val session = chatSessionService.getSessionById(sessionId, user.id!!)
        val sessionAgents = agentService.getSessionActiveAgents(session)
        val agentDtos = sessionAgents.map { it.toDto(true) }
        return ResponseEntity.ok(agentDtos)
    }

    /**
     * 刷新向量缓存
     */
    @PostMapping("/refresh-cache")
    fun refreshVectorCache(): ResponseEntity<Map<String, Any>> {
        agentService.refreshVectorCache()
        return ResponseEntity.ok(
            mapOf(
                "message" to "向量缓存已刷新",
                "agentCount" to agentService.agentVectorCache.size,
                "executionPlanCount" to agentService.executionPlanVectorCache.size
            )
        )
    }

    /**
     * 获取缓存状态
     */
    @GetMapping("/cache-status")
    fun getCacheStatus(): ResponseEntity<Map<String, Any>> {
        return ResponseEntity.ok(
            mapOf(
                "agentCount" to agentService.agentVectorCache.size,
                "executionPlanCount" to agentService.executionPlanVectorCache.size,
                "agents" to agentService.agentVectorCache.keys.toList()
            )
        )
    }

    /**
     * 实体转DTO
     */
    private fun Agent.toDto(isSubscribed: Boolean = false): AgentDto {
        return AgentDto(
            id = this.id,
            name = this.name,
            description = this.description,
            avatarUrl = this.avatarUrl,
            isActive = this.isActive,
            confidenceThreshold = this.confidenceThreshold,
            priorityOrder = this.priorityOrder,
            monthlyPrice = this.monthlyPrice,
            isDefault = this.isDefault,
            isSubscribed = isSubscribed,
            subscriptionStatus = if (isSubscribed) SubscriptionStatus.SUBSCRIBED else SubscriptionStatus.NOT_SUBSCRIBED,
            executionPlans = emptyList() // 避免循环引用，如需要可单独查询
        )
    }

    private fun UserAgentSubscription.toDto(): UserAgentSubscriptionDto {
        return UserAgentSubscriptionDto(
            id = this.id,
            agent = this.agent.toDto(true),
            isActive = this.isActive,
            subscribedAt = this.subscribedAt,
            expiresAt = this.expiresAt,
            status = when {
                !this.isActive -> SubscriptionStatus.EXPIRED
                this.expiresAt?.isBefore(java.time.LocalDateTime.now()) == true -> SubscriptionStatus.EXPIRED
                else -> SubscriptionStatus.SUBSCRIBED
            }
        )
    }

    private fun SessionAgent.toDto(): SessionAgentDto {
        return SessionAgentDto(
            id = this.id,
            agent = this.agent.toDto(true),
            isActive = this.isActive,
            addedAt = this.addedAt
        )
    }
}
