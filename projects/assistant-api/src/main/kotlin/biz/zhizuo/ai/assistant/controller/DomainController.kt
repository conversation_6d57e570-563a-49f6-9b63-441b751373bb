package biz.zhizuo.ai.assistant.controller

import biz.zhizuo.ai.assistant.dto.DomainDto
import biz.zhizuo.ai.assistant.entity.Domain
import biz.zhizuo.ai.assistant.repository.DomainRepository
import biz.zhizuo.ai.assistant.service.DomainService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

/**
 * 领域管理控制器
 */
@RestController
@RequestMapping("/api/domains")
class DomainController(
    private val domainRepository: DomainRepository,
    private val domainService: DomainService
) {

    /**
     * 获取所有活跃的领域
     */
    @GetMapping
    fun getAllActiveDomains(): ResponseEntity<List<DomainDto>> {
        val domains = domainRepository.findByIsActiveTrueOrderByPriorityOrderAsc()
        val domainDtos = domains.map { it.toDto() }
        return ResponseEntity.ok(domainDtos)
    }

    /**
     * 根据ID获取领域
     */
    @GetMapping("/{id}")
    fun getDomainById(@PathVariable id: String): ResponseEntity<DomainDto> {
        val domain = domainRepository.findById(id)
            .orElseThrow { IllegalArgumentException("领域不存在: $id") }
        return ResponseEntity.ok(domain.toDto())
    }

    /**
     * 刷新向量缓存
     */
    @PostMapping("/refresh-cache")
    fun refreshVectorCache(): ResponseEntity<Map<String, Any>> {
        domainService.refreshVectorCache()
        return ResponseEntity.ok(mapOf(
            "message" to "向量缓存已刷新",
            "domainCount" to domainService.domainVectorCache.size,
            "executionPlanCount" to domainService.executionPlanVectorCache.size
        ))
    }

    /**
     * 获取缓存状态
     */
    @GetMapping("/cache-status")
    fun getCacheStatus(): ResponseEntity<Map<String, Any>> {
        return ResponseEntity.ok(mapOf(
            "domainCount" to domainService.domainVectorCache.size,
            "executionPlanCount" to domainService.executionPlanVectorCache.size,
            "domains" to domainService.domainVectorCache.keys.toList()
        ))
    }

    /**
     * 实体转DTO
     */
    private fun Domain.toDto(): DomainDto {
        return DomainDto(
            id = this.id,
            name = this.name,
            description = this.description,
            isActive = this.isActive,
            confidenceThreshold = this.confidenceThreshold,
            priorityOrder = this.priorityOrder,
            executionPlans = emptyList() // 避免循环引用，如需要可单独查询
        )
    }
}
