package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.config.FloatArrayConverter
import jakarta.persistence.*
import org.hibernate.annotations.UuidGenerator
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * AI助手代理聚合根实体
 * 代表一个AI助手代理，如日程助理、美工、活动策划等
 */
@Entity
@Table(name = "agents")
@EntityListeners(AuditingEntityListener::class)
data class Agent(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @UuidGenerator(style = UuidGenerator.Style.TIME)
    val id: String? = null,

    @Column(nullable = false, length = 100, unique = true)
    val name: String, // Agent名称，如：日程助理、美工、活动策划等

    @Column(columnDefinition = "TEXT")
    val description: String? = null,

    @Column(name = "avatar_url", length = 500)
    val avatarUrl: String? = null, // Agent头像URL

    @Column(length = 65535)
    @Convert(converter = FloatArrayConverter::class)
    var embeddingVector: FloatArray? = null, // Agent的嵌入向量

    @Column(name = "is_active")
    val isActive: Boolean = true,

    @Column(name = "confidence_threshold")
    val confidenceThreshold: Double = 0.7, // Agent匹配的置信度阈值

    @Column(name = "priority_order")
    val priorityOrder: Int = 0, // 优先级，数字越小优先级越高

    @Column(name = "monthly_price")
    val monthlyPrice: Int = 0, // 月订阅价格（分）

    @Column(name = "is_default")
    val isDefault: Boolean = false, // 是否为默认Agent（免费）

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @OneToMany(mappedBy = "agent", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
    val executionPlans: List<ExecutionPlan> = emptyList()
)

/**
 * 执行计划实体
 * 属于某个Agent的具体执行方案
 */
@Entity
@Table(name = "execution_plans")
@EntityListeners(AuditingEntityListener::class)
data class ExecutionPlan(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @UuidGenerator(style = UuidGenerator.Style.TIME)
    val id: String? = null,

    @Column(nullable = false, length = 100)
    val name: String,

    @Column(columnDefinition = "TEXT")
    val description: String? = null,

    @Column(length = 65535)
    @Convert(converter = FloatArrayConverter::class)
    var embeddingVector: FloatArray? = null, // 执行计划的嵌入向量

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "agent_id", nullable = false)
    val agent: Agent, // 所属Agent

    @Column(name = "is_active")
    val isActive: Boolean = true,

    @Column(name = "priority_order")
    val priorityOrder: Int = 0, // 优先级，数字越小优先级越高

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @OneToMany(mappedBy = "executionPlan", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
    val steps: List<ExecutionPlanStep> = emptyList(),
)

/**
 * 执行计划步骤实体
 */
@Entity
@Table(name = "execution_plan_steps")
@EntityListeners(AuditingEntityListener::class)
data class ExecutionPlanStep(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @UuidGenerator(style = UuidGenerator.Style.TIME)
    val id: String? = null,

    @ManyToOne
    @JoinColumn(name = "execution_plan_id")
    val executionPlan: ExecutionPlan,

    @Column(nullable = false, length = 100)
    val name: String, // 步骤名称，如：理解问题、搜集资料等

    @Column(columnDefinition = "TEXT")
    val description: String? = null,

    @Column(name = "step_order", nullable = false)
    val stepOrder: Int, // 步骤顺序

    @Column(name = "model_name", length = 100)
    val modelName: String? = null, // 使用的模型名称

    @Column(name = "prompt_template", columnDefinition = "TEXT")
    val promptTemplate: String? = null, // 提示词模板

    @Column(name = "expected_duration_seconds")
    val expectedDurationSeconds: Int? = null, // 预期执行时长（秒）

    @Column(name = "is_parallel")
    val isParallel: Boolean = false, // 是否可以并行执行

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),
)

/**
 * 消息执行上下文实体
 */
@Entity
@Table(name = "message_execution_contexts")
@EntityListeners(AuditingEntityListener::class)
data class MessageExecutionContext(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @UuidGenerator(style = UuidGenerator.Style.TIME)
    val id: String? = null,

    @OneToOne
    @JoinColumn(name = "message_id")
    val message: ChatMessage,

    @ManyToOne
    @JoinColumn(name = "execution_plan_id")
    val executionPlan: ExecutionPlan,

    @Column(nullable = false, length = 50)
    val detectedDomain: String, // 检测到的问题领域

    @Column(name = "confidence_score")
    val confidenceScore: Double = 0.0, // 匹配置信度

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    val status: ExecutionStatus = ExecutionStatus.PENDING,

    @Column(name = "current_step_order")
    val currentStepOrder: Int = 0,

    @Column(name = "started_at")
    val startedAt: LocalDateTime? = null,

    @Column(name = "completed_at")
    val completedAt: LocalDateTime? = null,

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @OneToMany(mappedBy = "executionContext", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
    val stepExecutions: List<StepExecution> = emptyList(),
)

/**
 * 步骤执行记录实体
 */
@Entity
@Table(name = "step_executions")
@EntityListeners(AuditingEntityListener::class)
data class StepExecution(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @UuidGenerator(style = UuidGenerator.Style.TIME)
    val id: String? = null,

    @ManyToOne
    @JoinColumn(name = "execution_context_id")
    val executionContext: MessageExecutionContext,

    @ManyToOne
    @JoinColumn(name = "execution_plan_step_id")
    val executionPlanStep: ExecutionPlanStep,

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    val status: StepStatus = StepStatus.PENDING,

    @Column(name = "started_at")
    val startedAt: LocalDateTime? = null,

    @Column(name = "completed_at")
    val completedAt: LocalDateTime? = null,

    @Column(name = "duration_seconds")
    val durationSeconds: Int? = null,

    @Column(columnDefinition = "TEXT")
    val output: String? = null, // 步骤输出内容

    @Column(columnDefinition = "TEXT")
    val errorMessage: String? = null, // 错误信息

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),
)

/**
 * 用户Agent订阅实体
 */
@Entity
@Table(name = "user_agent_subscriptions")
@EntityListeners(AuditingEntityListener::class)
data class UserAgentSubscription(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @UuidGenerator(style = UuidGenerator.Style.TIME)
    val id: String? = null,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    val user: User,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "agent_id", nullable = false)
    val agent: Agent,

    @Column(name = "is_active")
    val isActive: Boolean = true,

    @Column(name = "subscribed_at", nullable = false)
    val subscribedAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "expires_at")
    val expiresAt: LocalDateTime? = null, // 订阅到期时间，null表示永久

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),
)

/**
 * 会话Agent关联实体
 */
@Entity
@Table(name = "session_agents")
@EntityListeners(AuditingEntityListener::class)
data class SessionAgent(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @UuidGenerator(style = UuidGenerator.Style.TIME)
    val id: String? = null,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "session_id", nullable = false)
    val session: ChatSession,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "agent_id", nullable = false)
    val agent: Agent,

    @Column(name = "is_active")
    val isActive: Boolean = true, // 是否在当前会话中激活

    @Column(name = "added_at", nullable = false)
    val addedAt: LocalDateTime = LocalDateTime.now(),

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),
)
