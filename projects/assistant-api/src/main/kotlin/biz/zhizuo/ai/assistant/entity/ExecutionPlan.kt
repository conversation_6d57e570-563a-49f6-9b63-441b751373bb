package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.config.FloatArrayConverter
import jakarta.persistence.*
import org.hibernate.annotations.UuidGenerator
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 领域聚合根实体
 * 代表一个问题领域，包含该领域的所有执行计划
 */
@Entity
@Table(name = "domains")
@EntityListeners(AuditingEntityListener::class)
data class Domain(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @UuidGenerator(style = UuidGenerator.Style.TIME)
    val id: String? = null,

    @Column(nullable = false, length = 100, unique = true)
    val name: String, // 领域名称，如：技术问题、生活咨询、学术研究等

    @Column(columnDefinition = "TEXT")
    val description: String? = null,

    @Column(length = 65535)
    @Convert(converter = FloatArrayConverter::class)
    var embeddingVector: FloatArray? = null, // 领域的嵌入向量

    @Column(name = "is_active")
    val isActive: Boolean = true,

    @Column(name = "confidence_threshold")
    val confidenceThreshold: Double = 0.7, // 领域匹配的置信度阈值

    @Column(name = "priority_order")
    val priorityOrder: Int = 0, // 优先级，数字越小优先级越高

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @OneToMany(mappedBy = "domain", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
    val executionPlans: List<ExecutionPlan> = emptyList()
)

/**
 * 执行计划实体
 * 属于某个领域的具体执行方案
 */
@Entity
@Table(name = "execution_plans")
@EntityListeners(AuditingEntityListener::class)
data class ExecutionPlan(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @UuidGenerator(style = UuidGenerator.Style.TIME)
    val id: String? = null,

    @Column(nullable = false, length = 100)
    val name: String,

    @Column(columnDefinition = "TEXT")
    val description: String? = null,

    @Column(length = 65535)
    @Convert(converter = FloatArrayConverter::class)
    var embeddingVector: FloatArray? = null, // 执行计划的嵌入向量

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "domain_id", nullable = false)
    val domain: Domain, // 所属领域

    @Column(name = "is_active")
    val isActive: Boolean = true,

    @Column(name = "priority_order")
    val priorityOrder: Int = 0, // 优先级，数字越小优先级越高

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @OneToMany(mappedBy = "executionPlan", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
    val steps: List<ExecutionPlanStep> = emptyList(),
)

/**
 * 执行计划步骤实体
 */
@Entity
@Table(name = "execution_plan_steps")
@EntityListeners(AuditingEntityListener::class)
data class ExecutionPlanStep(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @UuidGenerator(style = UuidGenerator.Style.TIME)
    val id: String? = null,

    @ManyToOne
    @JoinColumn(name = "execution_plan_id")
    val executionPlan: ExecutionPlan,

    @Column(nullable = false, length = 100)
    val name: String, // 步骤名称，如：理解问题、搜集资料等

    @Column(columnDefinition = "TEXT")
    val description: String? = null,

    @Column(name = "step_order", nullable = false)
    val stepOrder: Int, // 步骤顺序

    @Column(name = "model_name", length = 100)
    val modelName: String? = null, // 使用的模型名称

    @Column(name = "prompt_template", columnDefinition = "TEXT")
    val promptTemplate: String? = null, // 提示词模板

    @Column(name = "expected_duration_seconds")
    val expectedDurationSeconds: Int? = null, // 预期执行时长（秒）

    @Column(name = "is_parallel")
    val isParallel: Boolean = false, // 是否可以并行执行

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),
)

/**
 * 消息执行上下文实体
 */
@Entity
@Table(name = "message_execution_contexts")
@EntityListeners(AuditingEntityListener::class)
data class MessageExecutionContext(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @UuidGenerator(style = UuidGenerator.Style.TIME)
    val id: String? = null,

    @OneToOne
    @JoinColumn(name = "message_id")
    val message: ChatMessage,

    @ManyToOne
    @JoinColumn(name = "execution_plan_id")
    val executionPlan: ExecutionPlan,

    @Column(nullable = false, length = 50)
    val detectedDomain: String, // 检测到的问题领域

    @Column(name = "confidence_score")
    val confidenceScore: Double = 0.0, // 匹配置信度

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    val status: ExecutionStatus = ExecutionStatus.PENDING,

    @Column(name = "current_step_order")
    val currentStepOrder: Int = 0,

    @Column(name = "started_at")
    val startedAt: LocalDateTime? = null,

    @Column(name = "completed_at")
    val completedAt: LocalDateTime? = null,

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @OneToMany(mappedBy = "executionContext", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
    val stepExecutions: List<StepExecution> = emptyList(),
)

/**
 * 步骤执行记录实体
 */
@Entity
@Table(name = "step_executions")
@EntityListeners(AuditingEntityListener::class)
data class StepExecution(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @UuidGenerator(style = UuidGenerator.Style.TIME)
    val id: String? = null,

    @ManyToOne
    @JoinColumn(name = "execution_context_id")
    val executionContext: MessageExecutionContext,

    @ManyToOne
    @JoinColumn(name = "execution_plan_step_id")
    val executionPlanStep: ExecutionPlanStep,

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    val status: StepStatus = StepStatus.PENDING,

    @Column(name = "started_at")
    val startedAt: LocalDateTime? = null,

    @Column(name = "completed_at")
    val completedAt: LocalDateTime? = null,

    @Column(name = "duration_seconds")
    val durationSeconds: Int? = null,

    @Column(columnDefinition = "TEXT")
    val output: String? = null, // 步骤输出内容

    @Column(columnDefinition = "TEXT")
    val errorMessage: String? = null, // 错误信息

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),
)
