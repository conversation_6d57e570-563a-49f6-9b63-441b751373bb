package biz.zhizuo.ai.assistant.service

import biz.zhizuo.ai.assistant.dto.*
import biz.zhizuo.ai.assistant.entity.*
import biz.zhizuo.ai.assistant.repository.ChatMessageRepository
import biz.zhizuo.ai.assistant.repository.ExecutionPlanRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.hibernate.Hibernate
import org.springframework.ai.chat.client.ChatClient
import org.springframework.ai.chat.messages.AssistantMessage
import org.springframework.ai.chat.messages.Message
import org.springframework.ai.chat.messages.UserMessage
import org.springframework.ai.chat.model.ChatModel
import org.springframework.ai.chat.model.ChatResponse
import org.springframework.ai.chat.prompt.Prompt
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter
import reactor.core.publisher.Flux
import java.time.LocalDateTime

@Service
@Transactional
class ChatMessageService(
    private val userService: UserService,
    private val chatMessageRepository: ChatMessageRepository,
    private val chatSessionService: ChatSessionService,
    private val sseService: SseService,
    private val chatModel: ChatModel,
    private val domainAdaptationService: DomainAdaptationService,
    private val executionPlanService: ExecutionPlanService,
    private val domainRepository: DomainRepository,
    private val executionPlanRepository: ExecutionPlanRepository,
) {
    private val coroutineScope: CoroutineScope =
        CoroutineScope(Dispatchers.IO)

    /**
     * 注册 SSE Emitter
     */
    fun registerSseEmitter(key: String, emitter: SseEmitter) {
        sseService.registerSseEmitter(key, emitter)
    }

    /**
     * 发送消息事件
     */
    private fun sendMessageEvent(key: String, eventType: String, data: Any) {
        sseService.sendMessageEvent(key, eventType, data)
    }

    /**
     * 获取会话中的所有消息
     */
    @Transactional(readOnly = true)
    fun getSessionMessages(sessionId: String, userId: String): List<ChatMessageDto> {
        val messages = chatMessageRepository.findBySessionIdAndUserIdOrderByCreatedAtAsc(sessionId, userId)
        return messages.map { it.toDto() }
    }

    fun getMessageDtoById(messageId: String, userId: String? = null): ChatMessageDto {
        return getMessageById(messageId, userId).toDto()
    }

    /**
     * 根据ID获取消息
     */
    @Transactional(readOnly = true)
    fun getMessageById(messageId: String, userId: String? = null): ChatMessage {
        val message = userId?.let { chatMessageRepository.findByIdAndUserId(messageId, it) }
            ?: chatMessageRepository.findByIdOrNull(messageId)
            ?: throw ChatMessageNotFoundException(messageId)
        // Manually initialize lazy collections within the transaction
        // to prevent LazyInitializationException when accessed outside this session/transaction
        Hibernate.initialize(message.steps)
        Hibernate.initialize(message.attachments)
        // it.feedback is often mapped as EAGER or handled differently,
        // but if it's lazy and needed, initialize it too.
        // Hibernate.initialize(message.feedback)
        return message
    }

    /**
     * 创建用户消息并生成助理回复
     */
    fun createUserMessage(request: CreateUserMessageRequest, userId: String): CreateUserMessageResponse {
        val user = userService.getUserById(userId)
        val session = chatSessionService.getSessionById(request.sessionId, userId)
        // 创建用户消息
        val userMessage = ChatMessage(
            session = session,
            user = user,
            role = MessageRole.USER,
            content = request.content,
            replyTo = request.replyToId?.let {
                getMessageById(it, userId)
            }
        )
        val savedUserMessage = chatMessageRepository.save(userMessage)

        // 创建助理消息（初始状态为生成中）
        val assistantMessage = ChatMessage(
            session = session,
            user = user,
            role = MessageRole.ASSISTANT,
            content = "",
            isGenerating = true,
            assistantName = "智能助手",
            replyTo = savedUserMessage
        )
        val savedAssistantMessage = chatMessageRepository.save(assistantMessage)

        // 更新会话的最后消息时间
        chatSessionService.updateLastMessageTime(request.sessionId, LocalDateTime.now())

        // 异步生成回复
        coroutineScope.launch {
            generateReplyAndStreamContent(userMessage, savedAssistantMessage)
        }

        return CreateUserMessageResponse(
            userMessage = savedUserMessage.toDto(),
            assistantMessage = savedAssistantMessage.toDto()
        )
    }

    /**
     * 更新消息
     */
    fun updateMessage(messageId: String, request: UpdateMessageRequest, userId: String): ChatMessageDto {
        val existingMessage = getMessageById(messageId, userId)

        val updatedMessage = existingMessage.copy(
            content = request.content ?: existingMessage.content,
            liked = request.liked ?: existingMessage.liked,
            disliked = request.disliked ?: existingMessage.disliked,
        )

        val savedMessage = chatMessageRepository.save(updatedMessage)
        return savedMessage.toDto()
    }

    /**
     * 删除消息
     */
    fun deleteMessage(messageId: String, userId: String): Boolean {
        val message = chatMessageRepository.findByIdAndUserId(messageId, userId)
            ?: return false

        chatMessageRepository.delete(message)
        return true
    }

    /**
     * 点赞消息
     */
    fun likeMessage(messageId: String, userId: String): ChatMessageDto {
        val message = getMessageById(messageId, userId)

        val updatedMessage = message.copy(
            liked = true,
            disliked = false
        )

        val savedMessage = chatMessageRepository.save(updatedMessage)
        return savedMessage.toDto()
    }

    /**
     * 点踩消息
     */
    fun dislikeMessage(messageId: String, userId: String): ChatMessageDto {
        val message = getMessageById(messageId, userId)

        val updatedMessage = message.copy(
            liked = false,
            disliked = true
        )

        val savedMessage = chatMessageRepository.save(updatedMessage)
        return savedMessage.toDto()
    }

    /**
     * 添加消息反馈
     */
    fun addMessageFeedback(messageId: String, feedback: ChatMessageFeedbackDto, userId: String): ChatMessageDto {
        val message = getMessageById(messageId, userId)

        // TODO: 添加反馈
        // 这里简化处理，实际应该创建独立的反馈实体
        return message.toDto()
    }

    /**
     * 重新生成消息
     */
    fun regenerateMessage(request: RegenerateMessageRequest, userId: String): RegenerateMessageResponse {
        val user = userService.getUserById(userId)
        val session = chatSessionService.getSessionById(request.sessionId, userId)
        // 验证原消息存在
        val originalMessage = getMessageById(request.messageId, userId)

        // 创建新的助理消息
        val newAssistantMessage = ChatMessage(
            session = session,
            user = user,
            role = MessageRole.ASSISTANT,
            content = "",
            isGenerating = true,
            assistantName = "智能助手",
            replyTo = originalMessage.replyTo
        )
        val savedNewAssistantMessage = chatMessageRepository.save(newAssistantMessage)

        // 异步生成回复
        coroutineScope.launch {
            generateReplyAndStreamContent(originalMessage, savedNewAssistantMessage)
        }

        return RegenerateMessageResponse(
            assistantMessage = savedNewAssistantMessage.toDto()
        )
    }

    /**
     * 异步生成回复并流式传输内容
     */
    fun generateReplyAndStreamContent(userMessage: ChatMessage, assistantMessage: ChatMessage) {
        val messageId = assistantMessage.id!!
        val userId = userMessage.user.id!!
        val sseKey = "$userId:$messageId"

        val assistantMessageDto = assistantMessage.toDto()
        val sessionId = assistantMessageDto.sessionId

        try {
            // 1. 领域适配阶段
            sendMessageEvent(
                sseKey, "domain-adaptation", mapOf(
                    "messageId" to messageId,
                    "status" to "IN_PROGRESS",
                    "message" to "正在确定问题领域..."
                )
            )

            // 获取会话历史用于领域适配
            val sessionHistory = getSessionMessages(sessionId, userId)
                .filter { it.role != MessageRole.ASSISTANT || !it.isGenerating }
                .map { dto ->
                    // 这里需要从DTO转换回实体，简化处理
                    ChatMessage(
                        session = userMessage.session,
                        user = userMessage.user,
                        role = dto.role,
                        content = dto.content,
                        createdAt = dto.createdAt ?: LocalDateTime.now()
                    )
                }

            // 进行领域适配
            val adaptationResult = domainAdaptationService.adaptDomain(userMessage, sessionHistory)

            // 发送领域适配完成事件
            sendMessageEvent(
                sseKey, "domain-adaptation", mapOf(
                    "messageId" to messageId,
                    "status" to adaptationResult.status.name,
                    "result" to adaptationResult
                )
            )

            // 2. 根据适配结果决定下一步
            when (adaptationResult.status) {
                DomainAdaptationStatus.DOMAIN_CONFIRMED -> {
                    // 领域已确认，进行执行计划选择
                    handleDomainConfirmed(userMessage, adaptationResult, sessionHistory, sseKey, userId)
                }

                DomainAdaptationStatus.DOMAIN_UNCERTAIN -> {
                    // 领域不确定，需要与用户对话获取更多信息
                    handleDomainUncertain(userMessage, adaptationResult, sseKey)
                }

                else -> {
                    // 其他状态，暂时使用默认处理
                    handleDefaultFlow(userMessage, sseKey)
                }
            }

            // 4. 在执行计划完成后，开始流式生成最终回复
            coroutineScope.launch {
                // 等待执行计划完成（简化处理，实际应该监听执行完成事件）
                kotlinx.coroutines.delay(15000) // 等待15秒，让执行计划完成

                // 开始流式生成最终回复
                generateStreamingContent(userMessage, assistantMessage)
            }

        } catch (e: Exception) {
            sendMessageEvent(
                sseKey, "generation-error", mapOf(
                    "messageId" to messageId,
                    "error" to (e.message ?: "未知错误")
                )
            )
            failGeneration(messageId, e.message ?: "生成失败")
        }
    }

    /**
     * 处理领域已确认的情况
     */
    private fun handleDomainConfirmed(
        userMessage: ChatMessage,
        adaptationResult: DomainAdaptationResultDto,
        sessionHistory: List<ChatMessage>,
        sseKey: String,
        userId: String,
    ) {
        // 获取领域实体
        val domain = domainRepository.findById(adaptationResult.detectedDomain!!.id!!)
            .orElseThrow { IllegalArgumentException("领域不存在") }

        // 选择执行计划
        val planSelectionResult = domainAdaptationService.selectExecutionPlans(domain, userMessage, sessionHistory)

        // 发送执行计划选择事件
        sendMessageEvent(
            sseKey, "execution-plan-selection", mapOf(
                "messageId" to userMessage.id!!,
                "result" to planSelectionResult
            )
        )

        if (planSelectionResult.isConfident && planSelectionResult.recommendedPlan != null) {
            // 有明确推荐的执行计划，直接开始执行
            val executionPlan = executionPlanRepository.findById(planSelectionResult.recommendedPlan.id!!)
                .orElseThrow { IllegalArgumentException("执行计划不存在") }

            val executionContext = domainAdaptationService.createExecutionContext(userMessage, executionPlan, domain)
            executionPlanService.startExecution(executionContext.id!!, userId)
        } else {
            // 需要用户选择执行计划，发送选择请求
            sendMessageEvent(
                sseKey, "plan-selection-required", mapOf(
                    "messageId" to userMessage.id!!,
                    "availablePlans" to planSelectionResult.availablePlans,
                    "message" to (planSelectionResult.reasoning ?: "请选择合适的执行计划")
                )
            )
        }
    }

    /**
     * 处理领域不确定的情况
     */
    private fun handleDomainUncertain(
        userMessage: ChatMessage,
        adaptationResult: DomainAdaptationResultDto,
        sseKey: String,
    ) {
        // 发送需要更多信息的事件
        sendMessageEvent(
            sseKey, "domain-clarification-required", mapOf(
                "messageId" to userMessage.id!!,
                "alternatives" to adaptationResult.alternatives,
                "message" to (adaptationResult.reasoning ?: "我需要更多信息来确定问题领域"),
                "confidence" to adaptationResult.confidenceScore
            )
        )

        // 获取对应的助理消息
        val assistantMessage = chatMessageRepository.findByReplyToAndRole(userMessage, MessageRole.ASSISTANT)
            ?: throw IllegalStateException("找不到对应的助理消息")

        // 直接开始对话式回复，不执行具体的执行计划
        generateStreamingContent(
            userMessage, assistantMessage,
            "我需要更多信息来确定您的问题属于哪个领域。${adaptationResult.reasoning}"
        )
    }

    /**
     * 处理默认流程
     */
    private fun handleDefaultFlow(userMessage: ChatMessage, sseKey: String) {
        // 获取对应的助理消息
        val assistantMessage = chatMessageRepository.findByReplyToAndRole(userMessage, MessageRole.ASSISTANT)
            ?: throw IllegalStateException("找不到对应的助理消息")

        // 使用默认的通用对话流程
        generateStreamingContent(userMessage, assistantMessage)
    }

    /**
     * 处理流式内容生成（由ExecutionPlanService调用）
     */
    fun generateStreamingContent(
        userMessage: ChatMessage,
        assistantMessage: ChatMessage,
        prompt: String? = null,
    ) {
        val messageId = assistantMessage.id!!
        val userId = userMessage.user.id!!
        val sseKey = "$userId:$messageId"
        val sessionId = assistantMessage.session.id!!

        try {
            // 构建提示词
            val finalPrompt = prompt ?: userMessage.content
            val messagesForPrompt = getSpringAiMessagesForPrompt(sessionId, userId, messageId) +
                    UserMessage(finalPrompt)

            val chatClient = ChatClient.builder(chatModel).build()
            val promptObj = Prompt(messagesForPrompt)
            val chatResponseFlux: Flux<ChatResponse> = chatClient.prompt(promptObj).stream().chatResponse()

            val fullContent = StringBuilder()

            var chunkIndex = 0
            chatResponseFlux
                .doOnNext { chatResponse ->
                    chatResponse.results.forEach { generation ->
                        generation.output.text?.let { chunk ->
                            fullContent.append(chunk)

                            // 直接发送流式内容事件
                            sendMessageEvent(
                                sseKey, "streaming-content", mapOf(
                                    "messageId" to messageId,
                                    "text" to chunk,
                                    "index" to chunkIndex++
                                )
                            )
                        }
                    }
                }
                .doOnError { throwable ->
                    sendMessageEvent(
                        sseKey, "generation-error", mapOf(
                            "messageId" to messageId,
                            "error" to (throwable.message ?: "未知错误")
                        )
                    )
                    failGeneration(messageId, throwable.message ?: "AI生成失败")
                }
                .doOnComplete {
                    // 更新数据库中的消息内容
                    val completedMessage = completeGeneration(messageId, fullContent.toString())

                    sendMessageEvent(
                        sseKey, "generation-complete", mapOf(
                            "messageId" to messageId,
                            "message" to completedMessage
                        )
                    )
                }
                .subscribe()

        } catch (e: Exception) {
            sendMessageEvent(
                sseKey, "generation-error", mapOf(
                    "messageId" to messageId,
                    "error" to (e.message ?: "未知错误")
                )
            )
            failGeneration(messageId, e.message ?: "生成失败")
        }
    }

    /**
     * 开始消息生成
     */
    fun startGeneration(messageId: String, userId: String): Map<String, String> {
        val message = chatMessageRepository.findByIdAndUserId(messageId, userId)
            ?: return mapOf("error" to "消息不存在")

        if (message.role != MessageRole.ASSISTANT) {
            return mapOf("error" to "只能生成助理消息")
        }

        // 这里应该启动实际的AI生成过程
        // 目前返回成功消息
        return mapOf("message" to "开始生成消息")
    }

    /**
     * 完成消息生成
     */
    fun completeGeneration(messageId: String, content: String): ChatMessageDto {
        val message = getMessageById(messageId)

        val updatedMessage = message.copy(
            content = content,
            isGenerating = false
        )

        val savedMessage = chatMessageRepository.save(updatedMessage)
        return savedMessage.toDto()
    }

    /**
     * 标记消息生成失败
     */
    fun failGeneration(messageId: String, errorMessage: String): ChatMessageDto {
        val message = getMessageById(messageId)

        val updatedMessage = message.copy(
            content = "AI 生成失败: $errorMessage",
            isGenerating = false // 标记为非生成中状态
        )
        val savedMessage = chatMessageRepository.save(updatedMessage)
        return savedMessage.toDto()
    }

    /**
     * 获取用于回复的用户消息内容
     * 根据助理消息ID，找到它回复的用户消息，并返回其内容
     */
    @Transactional(readOnly = true)
    fun getUserMessageContentForReply(assistantMessageId: String, userId: String): String {
        val assistantMessage = chatMessageRepository.findByIdAndUserId(assistantMessageId, userId)
            ?: throw ChatMessageNotFoundException(assistantMessageId)
        val userMessage = assistantMessage.replyTo
            ?: throw UserMessageNotFoundException(assistantMessageId)
        return userMessage.content
    }

    /**
     * 获取用于 Spring AI Prompt 的会话消息列表
     * @param sessionId 会话ID
     * @param userId 用户ID
     * @param upToMessageId 可选，历史消息将截至（但不包括）此消息ID。通常是当前正在生成的助理消息ID。
     */
    @Transactional(readOnly = true)
    fun getSpringAiMessagesForPrompt(
        sessionId: String,
        userId: String,
        upToMessageId: String? = null,
    ): List<Message> {
        val historyDtos = getSessionMessages(sessionId, userId) // 依赖现有方法获取 DTOs
        val springAiMessages = mutableListOf<Message>()

        for (msgDto in historyDtos) {
            // 如果指定了 upToMessageId，则忽略此消息之后的所有消息
            if (upToMessageId != null && msgDto.id == upToMessageId) {
                break
            }

            when (msgDto.role) {
                MessageRole.USER -> springAiMessages.add(UserMessage(msgDto.content))
                MessageRole.ASSISTANT -> {
                    // 只包括已完成的、有内容的助理回复
                    if (!msgDto.isGenerating && msgDto.content.isNotBlank()) {
                        springAiMessages.add(AssistantMessage(msgDto.content))
                    }
                }

                MessageRole.SYSTEM -> {
                    throw IllegalStateException("历史记录中不应该记录系统消息")
                }
            }
        }
        return springAiMessages
    }


    /**
     * 删除会话的所有消息
     */
    fun deleteSessionMessages(sessionId: String) {
        chatMessageRepository.deleteBySessionId(sessionId)
    }

    /**
     * 删除用户的所有消息
     */
    fun deleteAllUserMessages(userId: String) {
        chatMessageRepository.deleteByUserId(userId)
    }

    /**
     * 实体转DTO
     */
    private fun ChatMessage.toDto(): ChatMessageDto {
        return ChatMessageDto(
            id = this.id,
            sessionId = this.session.id!!,
            role = this.role,
            content = this.content,
            isGenerating = this.isGenerating,
            replyToId = this.replyTo?.id,
            activeReplyId = this.activeReply?.id,
            assistantName = this.assistantName,
            liked = this.liked,
            disliked = this.disliked,
            createdAt = this.createdAt,
            steps = this.steps.map { it.toDto() },
            attachments = this.attachments.map { it.toDto() },
            feedback = this.feedback?.toDto(),
            replyIds = emptyList() // 需要额外查询
        )
    }

    private fun ChatMessageGenerationStep.toDto(): ChatMessageGenerationStepDto {
        return ChatMessageGenerationStepDto(
            id = this.id,
            step = this.step,
            description = this.description,
            stepOrder = this.stepOrder,
            status = this.status
        )
    }

    private fun Attachment.toDto(): AttachmentDto {
        return AttachmentDto(
            id = this.id,
            name = this.name,
            type = this.type,
            fileSize = this.fileSize,
            mimeType = this.mimeType,
            content = this.content,
            createdAt = this.createdAt
        )
    }

    private fun ChatMessageFeedback.toDto(): ChatMessageFeedbackDto {
        return ChatMessageFeedbackDto(
            id = this.id,
            rate = this.rate,
            content = this.content,
            createdAt = this.createdAt
        )
    }
}
