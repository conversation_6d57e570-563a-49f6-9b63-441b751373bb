package biz.zhizuo.ai.assistant.service

import biz.zhizuo.ai.assistant.dto.*
import biz.zhizuo.ai.assistant.entity.*
import biz.zhizuo.ai.assistant.repository.DomainRepository
import biz.zhizuo.ai.assistant.repository.ExecutionPlanRepository
import biz.zhizuo.ai.assistant.repository.MessageExecutionContextRepository
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * 领域适配服务
 * 负责分析用户消息，确定问题领域，并匹配合适的执行计划
 */
@Service
@Transactional
class DomainAdaptationService(
    private val domainService: DomainService,
    private val domainRepository: DomainRepository,
    private val executionPlanRepository: ExecutionPlanRepository,
    private val messageExecutionContextRepository: MessageExecutionContextRepository,
) {

    private val logger = LoggerFactory.getLogger(DomainAdaptationService::class.java)

    /**
     * 为用户消息进行领域适配
     */
    fun adaptDomain(userMessage: ChatMessage, sessionHistory: List<ChatMessage>): DomainAdaptationResultDto {
        logger.info("开始为消息 ${userMessage.id} 进行领域适配")

        // 使用DomainService进行领域匹配
        val domainMatchResult = domainService.matchDomain(userMessage, sessionHistory)

        return if (domainMatchResult.isConfident && domainMatchResult.domain != null) {
            // 领域确定，返回确认结果
            logger.info("领域确定: ${domainMatchResult.domain.name}, 置信度: ${domainMatchResult.confidence}")
            DomainAdaptationResultDto(
                detectedDomain = domainMatchResult.domain.toDto(),
                confidenceScore = domainMatchResult.confidence,
                isConfident = true,
                alternatives = domainMatchResult.alternatives.map { it.toDto() },
                reasoning = generateDomainReasoningExplanation(userMessage, domainMatchResult.domain),
                status = DomainAdaptationStatus.DOMAIN_CONFIRMED
            )
        } else {
            // 领域不确定，需要更多信息
            logger.info("领域不确定，置信度: ${domainMatchResult.confidence}")
            DomainAdaptationResultDto(
                detectedDomain = null,
                confidenceScore = domainMatchResult.confidence,
                isConfident = false,
                alternatives = domainMatchResult.alternatives.map { it.toDto() },
                reasoning = "根据您的问题，我需要更多信息来确定具体的问题领域。",
                status = DomainAdaptationStatus.DOMAIN_UNCERTAIN
            )
        }
    }

    /**
     * 为确定的领域选择执行计划
     */
    fun selectExecutionPlans(domain: Domain, userMessage: ChatMessage, sessionHistory: List<ChatMessage>): ExecutionPlanSelectionResultDto {
        logger.info("开始为领域 ${domain.name} 选择执行计划")

        // 使用DomainService进行执行计划匹配
        val planMatchResult = domainService.matchExecutionPlans(domain, userMessage, sessionHistory)

        return if (planMatchResult.isConfident && planMatchResult.executionPlans.isNotEmpty()) {
            // 有明确的推荐计划
            val recommendedPlan = planMatchResult.executionPlans.first()
            logger.info("推荐执行计划: ${recommendedPlan.name}, 置信度: ${planMatchResult.confidence}")

            ExecutionPlanSelectionResultDto(
                availablePlans = planMatchResult.executionPlans.map { it.toDto() },
                recommendedPlan = recommendedPlan.toDto(),
                confidenceScore = planMatchResult.confidence,
                isConfident = true,
                reasoning = generatePlanReasoningExplanation(userMessage, recommendedPlan)
            )
        } else {
            // 没有明确推荐，提供所有可用计划供用户选择
            logger.info("没有明确推荐的执行计划，提供所有可用计划")

            ExecutionPlanSelectionResultDto(
                availablePlans = planMatchResult.executionPlans.map { it.toDto() },
                recommendedPlan = null,
                confidenceScore = planMatchResult.confidence,
                isConfident = false,
                reasoning = "根据您的问题，我为您提供了几个可能的解决方案，请选择最适合的一个。"
            )
        }
    }

    /**
     * 创建消息执行上下文
     */
    fun createExecutionContext(
        userMessage: ChatMessage,
        executionPlan: ExecutionPlan,
        detectedDomain: Domain
    ): MessageExecutionContextDto {
        val executionContext = MessageExecutionContext(
            message = userMessage,
            executionPlan = executionPlan,
            detectedDomain = detectedDomain.name,
            confidenceScore = 1.0, // 用户确认后置信度为1.0
            status = ExecutionStatus.PENDING,
            currentStepOrder = 0
        )

        val savedContext = messageExecutionContextRepository.save(executionContext)
        logger.info("创建执行上下文: ${savedContext.id}")

        return savedContext.toDto()
    }

    /**
     * 生成领域推理解释
     */
    private fun generateDomainReasoningExplanation(userMessage: ChatMessage, domain: Domain): String {
        return "根据您的问题「${userMessage.content}」，我判断这属于「${domain.name}」领域。"
    }

    /**
     * 生成执行计划推理解释
     */
    private fun generatePlanReasoningExplanation(userMessage: ChatMessage, executionPlan: ExecutionPlan): String {
        return "基于您的问题「${userMessage.content}」，我推荐使用「${executionPlan.name}」来处理。"
    }

    /**
     * 实体转DTO
     */
    private fun Domain.toDto(): DomainDto {
        return DomainDto(
            id = this.id,
            name = this.name,
            description = this.description,
            isActive = this.isActive,
            confidenceThreshold = this.confidenceThreshold,
            priorityOrder = this.priorityOrder,
            executionPlans = emptyList() // 避免循环引用
        )
    }

    private fun ExecutionPlan.toDto(): ExecutionPlanDto {
        return ExecutionPlanDto(
            id = this.id,
            name = this.name,
            description = this.description,
            domain = this.domain.toDto(),
            isActive = this.isActive,
            priorityOrder = this.priorityOrder,
            steps = this.steps.map { it.toDto() }
        )
    }

    private fun ExecutionPlanStep.toDto(): ExecutionPlanStepDto {
        return ExecutionPlanStepDto(
            id = this.id,
            name = this.name,
            description = this.description,
            stepOrder = this.stepOrder,
            modelName = this.modelName,
            expectedDurationSeconds = this.expectedDurationSeconds,
            isParallel = this.isParallel
        )
    }

    private fun MessageExecutionContext.toDto(): MessageExecutionContextDto {
        return MessageExecutionContextDto(
            id = this.id,
            messageId = this.message.id!!,
            executionPlan = this.executionPlan.toDto(),
            detectedDomain = this.detectedDomain,
            confidenceScore = this.confidenceScore,
            status = this.status,
            currentStepOrder = this.currentStepOrder,
            startedAt = this.startedAt,
            completedAt = this.completedAt,
            stepExecutions = this.stepExecutions.map { it.toDto() }
        )
    }

    private fun StepExecution.toDto(): StepExecutionDto {
        return StepExecutionDto(
            id = this.id,
            executionPlanStep = this.executionPlanStep.toDto(),
            status = this.status,
            startedAt = this.startedAt,
            completedAt = this.completedAt,
            durationSeconds = this.durationSeconds,
            output = this.output,
            errorMessage = this.errorMessage
        )
    }
}
