package biz.zhizuo.ai.assistant.dto

import biz.zhizuo.ai.assistant.entity.ExecutionStatus
import biz.zhizuo.ai.assistant.entity.StepStatus
import java.time.LocalDateTime

/**
 * Agent DTO
 */
data class AgentDto(
    val id: String?,
    val name: String,
    val description: String?,
    val avatarUrl: String?,
    val isActive: Boolean,
    val confidenceThreshold: Double,
    val priorityOrder: Int,
    val monthlyPrice: Int, // 月订阅价格（分）
    val isDefault: Boolean, // 是否为默认Agent（免费）
    val isSubscribed: Boolean = false, // 用户是否已订阅
    val subscriptionStatus: SubscriptionStatus = SubscriptionStatus.NOT_SUBSCRIBED,
    val executionPlans: List<ExecutionPlanDto> = emptyList()
)

/**
 * 执行计划 DTO
 */
data class ExecutionPlanDto(
    val id: String?,
    val name: String,
    val description: String?,
    val agent: AgentDto,
    val isActive: <PERSON><PERSON><PERSON>,
    val priorityOrder: Int,
    val steps: List<ExecutionPlanStepDto> = emptyList()
)

/**
 * 订阅状态枚举
 */
enum class SubscriptionStatus {
    NOT_SUBSCRIBED, // 未订阅
    SUBSCRIBED, // 已订阅
    EXPIRED // 已过期
}

/**
 * 执行计划步骤 DTO
 */
data class ExecutionPlanStepDto(
    val id: String?,
    val name: String,
    val description: String?,
    val stepOrder: Int,
    val modelName: String?,
    val expectedDurationSeconds: Int?,
    val isParallel: Boolean
)

/**
 * 消息执行上下文 DTO
 */
data class MessageExecutionContextDto(
    val id: String?,
    val messageId: String,
    val executionPlan: ExecutionPlanDto,
    val detectedDomain: String,
    val confidenceScore: Double,
    val status: ExecutionStatus,
    val currentStepOrder: Int,
    val startedAt: LocalDateTime?,
    val completedAt: LocalDateTime?,
    val stepExecutions: List<StepExecutionDto> = emptyList()
)

/**
 * 步骤执行记录 DTO
 */
data class StepExecutionDto(
    val id: String?,
    val executionPlanStep: ExecutionPlanStepDto,
    val status: StepStatus,
    val startedAt: LocalDateTime?,
    val completedAt: LocalDateTime?,
    val durationSeconds: Int?,
    val output: String?,
    val errorMessage: String?
)

/**
 * Agent匹配结果 DTO
 */
data class AgentMatchResultDto(
    val matchedAgents: List<AgentDto>, // 匹配的Agent列表
    val allAgents: List<AgentScoreDto>, // 所有Agent的评分
    val hasConfidentMatch: Boolean, // 是否有高置信度匹配
    val reasoning: String? = null // 匹配原因
)

/**
 * Agent评分 DTO
 */
data class AgentScoreDto(
    val agent: AgentDto,
    val similarity: Double,
    val isActive: Boolean = true // 在当前会话中是否激活
)

/**
 * 执行计划选择结果 DTO
 */
data class ExecutionPlanSelectionResultDto(
    val availablePlans: List<ExecutionPlanDto>, // 可用的执行计划
    val recommendedPlan: ExecutionPlanDto?, // 推荐的执行计划
    val confidenceScore: Double,
    val isConfident: Boolean, // 是否达到置信度阈值
    val reasoning: String? = null // 推荐原因
)

/**
 * 用户Agent订阅 DTO
 */
data class UserAgentSubscriptionDto(
    val id: String?,
    val agent: AgentDto,
    val isActive: Boolean,
    val subscribedAt: LocalDateTime,
    val expiresAt: LocalDateTime?,
    val status: SubscriptionStatus
)

/**
 * 会话Agent DTO
 */
data class SessionAgentDto(
    val id: String?,
    val agent: AgentDto,
    val isActive: Boolean, // 是否在当前会话中激活
    val addedAt: LocalDateTime
)

/**
 * 执行计划更新事件 DTO
 */
data class ExecutionPlanUpdateDto(
    val messageId: String,
    val executionContext: MessageExecutionContextDto,
    val eventType: String, // "plan-generated", "step-started", "step-completed", "step-failed", "execution-completed"
    val currentStep: StepExecutionDto? = null,
    val message: String? = null
)

/**
 * 打字效果消息块 DTO
 */
data class TypingMessageChunkDto(
    val messageId: String,
    val chunkIndex: Int,
    val text: String,
    val isComplete: Boolean = false,
    val timestamp: LocalDateTime = LocalDateTime.now()
)

/**
 * 创建执行计划请求 DTO
 */
data class CreateExecutionPlanRequest(
    val name: String,
    val description: String?,
    val domain: String,
    val priorityOrder: Int = 0,
    val steps: List<CreateExecutionPlanStepRequest>
)

/**
 * 创建执行计划步骤请求 DTO
 */
data class CreateExecutionPlanStepRequest(
    val name: String,
    val description: String?,
    val stepOrder: Int,
    val modelName: String?,
    val promptTemplate: String?,
    val expectedDurationSeconds: Int?,
    val isParallel: Boolean = false
)
