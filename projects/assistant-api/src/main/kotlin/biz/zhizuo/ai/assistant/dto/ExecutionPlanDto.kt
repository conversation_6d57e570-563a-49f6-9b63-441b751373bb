package biz.zhizuo.ai.assistant.dto

import biz.zhizuo.ai.assistant.entity.ExecutionStatus
import biz.zhizuo.ai.assistant.entity.StepStatus
import java.time.LocalDateTime

/**
 * 领域 DTO
 */
data class DomainDto(
    val id: String?,
    val name: String,
    val description: String?,
    val isActive: Boolean,
    val confidenceThreshold: Double,
    val priorityOrder: Int,
    val executionPlans: List<ExecutionPlanDto> = emptyList()
)

/**
 * 执行计划 DTO
 */
data class ExecutionPlanDto(
    val id: String?,
    val name: String,
    val description: String?,
    val domain: DomainDto,
    val isActive: Boolean,
    val priorityOrder: Int,
    val steps: List<ExecutionPlanStepDto> = emptyList()
)

/**
 * 执行计划步骤 DTO
 */
data class ExecutionPlanStepDto(
    val id: String?,
    val name: String,
    val description: String?,
    val stepOrder: Int,
    val modelName: String?,
    val expectedDurationSeconds: Int?,
    val isParallel: Boolean
)

/**
 * 消息执行上下文 DTO
 */
data class MessageExecutionContextDto(
    val id: String?,
    val messageId: String,
    val executionPlan: ExecutionPlanDto,
    val detectedDomain: String,
    val confidenceScore: Double,
    val status: ExecutionStatus,
    val currentStepOrder: Int,
    val startedAt: LocalDateTime?,
    val completedAt: LocalDateTime?,
    val stepExecutions: List<StepExecutionDto> = emptyList()
)

/**
 * 步骤执行记录 DTO
 */
data class StepExecutionDto(
    val id: String?,
    val executionPlanStep: ExecutionPlanStepDto,
    val status: StepStatus,
    val startedAt: LocalDateTime?,
    val completedAt: LocalDateTime?,
    val durationSeconds: Int?,
    val output: String?,
    val errorMessage: String?
)

/**
 * 领域适配结果 DTO
 */
data class DomainAdaptationResultDto(
    val detectedDomain: DomainDto?, // 检测到的领域，null表示未确定
    val confidenceScore: Double,
    val isConfident: Boolean, // 是否达到置信度阈值
    val alternatives: List<DomainDto> = emptyList(), // 备选领域
    val reasoning: String? = null, // 选择该领域的原因
    val status: DomainAdaptationStatus = DomainAdaptationStatus.PENDING // 适配状态
)

/**
 * 执行计划选择结果 DTO
 */
data class ExecutionPlanSelectionResultDto(
    val availablePlans: List<ExecutionPlanDto>, // 可用的执行计划
    val recommendedPlan: ExecutionPlanDto?, // 推荐的执行计划
    val confidenceScore: Double,
    val isConfident: Boolean, // 是否达到置信度阈值
    val reasoning: String? = null // 推荐原因
)

/**
 * 领域适配状态枚举
 */
enum class DomainAdaptationStatus {
    PENDING, // 待处理
    IN_PROGRESS, // 进行中
    DOMAIN_UNCERTAIN, // 领域不确定，需要更多信息
    DOMAIN_CONFIRMED, // 领域已确认
    PLAN_SELECTION, // 执行计划选择中
    PLAN_CONFIRMED, // 执行计划已确认
    COMPLETED // 完成
}

/**
 * 执行计划更新事件 DTO
 */
data class ExecutionPlanUpdateDto(
    val messageId: String,
    val executionContext: MessageExecutionContextDto,
    val eventType: String, // "plan-generated", "step-started", "step-completed", "step-failed", "execution-completed"
    val currentStep: StepExecutionDto? = null,
    val message: String? = null
)

/**
 * 打字效果消息块 DTO
 */
data class TypingMessageChunkDto(
    val messageId: String,
    val chunkIndex: Int,
    val text: String,
    val isComplete: Boolean = false,
    val timestamp: LocalDateTime = LocalDateTime.now()
)

/**
 * 创建执行计划请求 DTO
 */
data class CreateExecutionPlanRequest(
    val name: String,
    val description: String?,
    val domain: String,
    val priorityOrder: Int = 0,
    val steps: List<CreateExecutionPlanStepRequest>
)

/**
 * 创建执行计划步骤请求 DTO
 */
data class CreateExecutionPlanStepRequest(
    val name: String,
    val description: String?,
    val stepOrder: Int,
    val modelName: String?,
    val promptTemplate: String?,
    val expectedDurationSeconds: Int?,
    val isParallel: Boolean = false
)
