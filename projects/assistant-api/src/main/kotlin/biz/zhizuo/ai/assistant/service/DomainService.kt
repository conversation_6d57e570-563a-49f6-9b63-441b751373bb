package biz.zhizuo.ai.assistant.service

import biz.zhizuo.ai.assistant.entity.*
import biz.zhizuo.ai.assistant.repository.DomainRepository
import biz.zhizuo.ai.assistant.repository.ExecutionPlanRepository
import org.slf4j.LoggerFactory
import org.springframework.ai.embedding.EmbeddingModel
import org.springframework.ai.embedding.EmbeddingRequest
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import jakarta.annotation.PostConstruct
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.sqrt

/**
 * 领域服务
 * 负责管理领域的向量匹配和领域识别
 */
@Service
@Transactional
class DomainService(
    private val domainRepository: DomainRepository,
    private val executionPlanRepository: ExecutionPlanRepository,
    private val embeddingModel: EmbeddingModel,
) {

    private val logger = LoggerFactory.getLogger(DomainService::class.java)

    // 内存中的领域向量缓存
    internal val domainVectorCache = ConcurrentHashMap<String, FloatArray>()

    // 内存中的执行计划向量缓存
    internal val executionPlanVectorCache = ConcurrentHashMap<String, FloatArray>()

    // 默认置信度阈值
    private val defaultConfidenceThreshold = 0.7

    /**
     * 初始化时加载所有领域和执行计划的向量到内存
     */
    @PostConstruct
    fun initializeVectorCache() {
        logger.info("开始初始化向量缓存...")

        try {
            // 加载所有活跃的领域向量
            val activeDomains = domainRepository.findAllActiveWithEmbedding()
            activeDomains.forEach { domain ->
                domain.embeddingVector?.let { vector ->
                    domainVectorCache[domain.id!!] = vector
                    logger.debug("加载领域向量: ${domain.name}")
                }
            }

            // 加载所有活跃的执行计划向量
            val activeExecutionPlans = executionPlanRepository.findByIsActiveTrueOrderByPriorityOrderAsc()
            activeExecutionPlans.forEach { plan ->
                plan.embeddingVector?.let { vector ->
                    executionPlanVectorCache[plan.id!!] = vector
                    logger.debug("加载执行计划向量: ${plan.name}")
                }
            }

            logger.info("向量缓存初始化完成，领域数量: ${domainVectorCache.size}, 执行计划数量: ${executionPlanVectorCache.size}")
        } catch (e: Exception) {
            logger.error("向量缓存初始化失败", e)
        }
    }

    /**
     * 刷新向量缓存
     */
    fun refreshVectorCache() {
        domainVectorCache.clear()
        executionPlanVectorCache.clear()
        initializeVectorCache()
    }

    /**
     * 根据用户消息匹配最合适的领域
     */
    fun matchDomain(userMessage: ChatMessage, sessionHistory: List<ChatMessage>): DomainMatchResult {
        logger.info("开始为消息 ${userMessage.id} 匹配领域")

        // 生成查询向量
        val queryVector = generateQueryVector(userMessage, sessionHistory)

        if (queryVector.isEmpty()) {
            logger.warn("无法生成查询向量，使用默认领域")
            return getDefaultDomainResult()
        }

        // 在内存中进行向量相似度计算
        val domainScores = mutableListOf<DomainScore>()

        domainVectorCache.forEach { (domainId, domainVector) ->
            val similarity = calculateCosineSimilarity(queryVector, domainVector)
            val domain = domainRepository.findById(domainId).orElse(null)
            if (domain != null) {
                domainScores.add(DomainScore(domain, similarity))
            }
        }

        // 按相似度排序
        domainScores.sortByDescending { it.similarity }

        if (domainScores.isEmpty()) {
            logger.warn("没有找到匹配的领域，使用默认领域")
            return getDefaultDomainResult()
        }

        val bestMatch = domainScores.first()
        val confidenceThreshold = bestMatch.domain.confidenceThreshold

        logger.info("最佳匹配领域: ${bestMatch.domain.name}, 相似度: ${bestMatch.similarity}, 阈值: $confidenceThreshold")

        return if (bestMatch.similarity >= confidenceThreshold) {
            DomainMatchResult(
                domain = bestMatch.domain,
                confidence = bestMatch.similarity,
                isConfident = true,
                alternatives = domainScores.take(3).map { it.domain }
            )
        } else {
            DomainMatchResult(
                domain = null,
                confidence = bestMatch.similarity,
                isConfident = false,
                alternatives = domainScores.take(3).map { it.domain }
            )
        }
    }

    /**
     * 根据领域匹配最合适的执行计划
     */
    fun matchExecutionPlans(domain: Domain, userMessage: ChatMessage, sessionHistory: List<ChatMessage>): ExecutionPlanMatchResult {
        logger.info("开始为领域 ${domain.name} 匹配执行计划")

        // 生成查询向量
        val queryVector = generateQueryVector(userMessage, sessionHistory)

        if (queryVector.isEmpty()) {
            logger.warn("无法生成查询向量，使用默认执行计划")
            return getDefaultExecutionPlanResult(domain)
        }

        // 获取该领域下的所有执行计划
        val domainExecutionPlans = executionPlanRepository.findActiveWithEmbeddingByDomain(domain)

        if (domainExecutionPlans.isEmpty()) {
            logger.warn("领域 ${domain.name} 下没有可用的执行计划")
            return ExecutionPlanMatchResult(
                executionPlans = emptyList(),
                confidence = 0.0,
                isConfident = false
            )
        }

        // 计算相似度
        val planScores = mutableListOf<ExecutionPlanScore>()

        domainExecutionPlans.forEach { plan ->
            plan.embeddingVector?.let { planVector ->
                val similarity = calculateCosineSimilarity(queryVector, planVector)
                planScores.add(ExecutionPlanScore(plan, similarity))
            }
        }

        // 按相似度排序
        planScores.sortByDescending { it.similarity }

        if (planScores.isEmpty()) {
            return getDefaultExecutionPlanResult(domain)
        }

        val bestMatch = planScores.first()
        val isConfident = bestMatch.similarity >= defaultConfidenceThreshold

        logger.info("最佳匹配执行计划: ${bestMatch.executionPlan.name}, 相似度: ${bestMatch.similarity}")

        return ExecutionPlanMatchResult(
            executionPlans = planScores.map { it.executionPlan },
            confidence = bestMatch.similarity,
            isConfident = isConfident
        )
    }

    /**
     * 生成查询向量
     */
    private fun generateQueryVector(userMessage: ChatMessage, sessionHistory: List<ChatMessage>): FloatArray {
        // 构建查询文本：当前消息 + 最近的会话历史
        val queryText = buildString {
            append(userMessage.content)

            // 添加最近3条历史消息作为上下文
            sessionHistory.takeLast(3).forEach { msg ->
                append(" ")
                append(msg.content)
            }
        }

        return try {
            // 使用嵌入模型生成向量
            val embeddingRequest = EmbeddingRequest(listOf(queryText), null)
            val embeddingResponse = embeddingModel.call(embeddingRequest)
            embeddingResponse.results.first().output.toFloatArray()
        } catch (e: Exception) {
            logger.warn("生成向量嵌入失败: ${e.message}")
            floatArrayOf() // 返回空数组
        }
    }

    /**
     * 计算余弦相似度
     */
    private fun calculateCosineSimilarity(vector1: FloatArray, vector2: FloatArray): Double {
        if (vector1.size != vector2.size) {
            return 0.0
        }

        var dotProduct = 0.0
        var norm1 = 0.0
        var norm2 = 0.0

        for (i in vector1.indices) {
            dotProduct += vector1[i] * vector2[i]
            norm1 += vector1[i] * vector1[i]
            norm2 += vector2[i] * vector2[i]
        }

        return if (norm1 == 0.0 || norm2 == 0.0) {
            0.0
        } else {
            dotProduct / (sqrt(norm1) * sqrt(norm2))
        }
    }

    /**
     * 获取默认领域结果
     */
    private fun getDefaultDomainResult(): DomainMatchResult {
        val defaultDomain = domainRepository.findByIsActiveTrueOrderByPriorityOrderAsc().firstOrNull()
        return DomainMatchResult(
            domain = defaultDomain,
            confidence = 0.0,
            isConfident = false,
            alternatives = emptyList()
        )
    }

    /**
     * 获取默认执行计划结果
     */
    private fun getDefaultExecutionPlanResult(domain: Domain): ExecutionPlanMatchResult {
        val defaultPlans = executionPlanRepository.findByDomainAndIsActiveTrueOrderByPriorityOrderAsc(domain)
        return ExecutionPlanMatchResult(
            executionPlans = defaultPlans,
            confidence = 0.0,
            isConfident = false
        )
    }
}

/**
 * 领域匹配结果
 */
data class DomainMatchResult(
    val domain: Domain?, // 匹配的领域，null表示未确定
    val confidence: Double, // 匹配置信度
    val isConfident: Boolean, // 是否达到置信度阈值
    val alternatives: List<Domain> // 备选领域
)

/**
 * 执行计划匹配结果
 */
data class ExecutionPlanMatchResult(
    val executionPlans: List<ExecutionPlan>, // 匹配的执行计划列表
    val confidence: Double, // 匹配置信度
    val isConfident: Boolean // 是否达到置信度阈值
)

/**
 * 领域评分
 */
private data class DomainScore(
    val domain: Domain,
    val similarity: Double
)

/**
 * 执行计划评分
 */
private data class ExecutionPlanScore(
    val executionPlan: ExecutionPlan,
    val similarity: Double
)
