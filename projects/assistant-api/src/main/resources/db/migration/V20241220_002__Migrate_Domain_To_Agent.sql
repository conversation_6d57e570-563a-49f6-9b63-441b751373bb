-- 重构Domain为Agent的数据库迁移脚本

-- 1. 创建agents表
CREATE TABLE agents (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    avatar_url VARCHAR(500),
    embedding_vector LONGTEXT, -- 存储向量的JSON格式
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    confidence_threshold DOUBLE NOT NULL DEFAULT 0.7,
    priority_order INT NOT NULL DEFAULT 0,
    monthly_price INT NOT NULL DEFAULT 0, -- 月订阅价格（分）
    is_default BOOLEAN NOT NULL DEFAULT FALSE, -- 是否为默认Agent（免费）
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 为agents表创建索引
CREATE INDEX idx_agents_active_priority ON agents(is_active, priority_order);
CREATE INDEX idx_agents_name ON agents(name);
CREATE INDEX idx_agents_default ON agents(is_default, is_active);

-- 2. 创建用户Agent订阅表
CREATE TABLE user_agent_subscriptions (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    agent_id VARCHAR(36) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    subscribed_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL, -- 订阅到期时间，null表示永久
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_user_agent_subscriptions_user 
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_user_agent_subscriptions_agent 
        FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_agent_active (user_id, agent_id, is_active)
);

-- 为用户Agent订阅表创建索引
CREATE INDEX idx_user_agent_subscriptions_user ON user_agent_subscriptions(user_id, is_active);
CREATE INDEX idx_user_agent_subscriptions_agent ON user_agent_subscriptions(agent_id);
CREATE INDEX idx_user_agent_subscriptions_expires ON user_agent_subscriptions(expires_at);

-- 3. 创建会话Agent关联表
CREATE TABLE session_agents (
    id VARCHAR(36) PRIMARY KEY,
    session_id VARCHAR(36) NOT NULL,
    agent_id VARCHAR(36) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    added_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_session_agents_session 
        FOREIGN KEY (session_id) REFERENCES chat_sessions(id) ON DELETE CASCADE,
    CONSTRAINT fk_session_agents_agent 
        FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    UNIQUE KEY uk_session_agent (session_id, agent_id)
);

-- 为会话Agent关联表创建索引
CREATE INDEX idx_session_agents_session ON session_agents(session_id, is_active);
CREATE INDEX idx_session_agents_agent ON session_agents(agent_id);

-- 4. 迁移domains数据到agents表
INSERT INTO agents (id, name, description, embedding_vector, is_active, confidence_threshold, priority_order, monthly_price, is_default)
SELECT 
    id,
    name,
    description,
    embedding_vector,
    is_active,
    confidence_threshold,
    priority_order,
    CASE 
        WHEN name = '通用问题' THEN 0
        WHEN name = '日程助理' THEN 1000  -- 10元/月
        WHEN name = '美工' THEN 20000     -- 200元/月
        WHEN name = '活动策划' THEN 20000  -- 200元/月
        WHEN name = '商情搜集' THEN 100000 -- 1000元/月
        ELSE 0
    END as monthly_price,
    CASE 
        WHEN name = '通用问题' THEN TRUE
        ELSE FALSE
    END as is_default
FROM domains;

-- 5. 更新agents表，设置头像URL
UPDATE agents SET avatar_url = '/assets/avatars/general-assistant.png' WHERE name = '通用问题';
UPDATE agents SET avatar_url = '/assets/avatars/schedule-assistant.png' WHERE name = '日程助理';
UPDATE agents SET avatar_url = '/assets/avatars/designer.png' WHERE name = '美工';
UPDATE agents SET avatar_url = '/assets/avatars/event-planner.png' WHERE name = '活动策划';
UPDATE agents SET avatar_url = '/assets/avatars/market-researcher.png' WHERE name = '商情搜集';

-- 6. 插入默认的Agent数据（如果不存在）
INSERT IGNORE INTO agents (id, name, description, avatar_url, is_active, confidence_threshold, priority_order, monthly_price, is_default) VALUES
('agent-general', '通用助手', '处理一般性问题和日常咨询的智能助手', '/assets/avatars/general-assistant.png', TRUE, 0.6, 100, 0, TRUE),
('agent-schedule', '日程助理', '专业的日程管理和时间规划助手，帮您高效安排时间', '/assets/avatars/schedule-assistant.png', TRUE, 0.7, 10, 1000, FALSE),
('agent-designer', '美工', '专业的视觉设计师，提供创意设计和美术指导', '/assets/avatars/designer.png', TRUE, 0.7, 20, 20000, FALSE),
('agent-event-planner', '活动策划', '经验丰富的活动策划专家，为您打造完美活动', '/assets/avatars/event-planner.png', TRUE, 0.7, 30, 20000, FALSE),
('agent-market-researcher', '商情搜集', '专业的市场研究分析师，提供深度商业洞察', '/assets/avatars/market-researcher.png', TRUE, 0.8, 40, 100000, FALSE);

-- 7. 修改execution_plans表，将domain_id改为agent_id
ALTER TABLE execution_plans 
DROP FOREIGN KEY fk_execution_plans_domain,
ADD COLUMN agent_id VARCHAR(36),
ADD CONSTRAINT fk_execution_plans_agent 
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE;

-- 8. 迁移execution_plans的domain_id到agent_id
UPDATE execution_plans ep 
SET agent_id = (
    SELECT a.id 
    FROM agents a 
    WHERE a.name = (
        SELECT d.name 
        FROM domains d 
        WHERE d.id = ep.domain_id
    )
    LIMIT 1
)
WHERE ep.domain_id IS NOT NULL;

-- 9. 为所有用户订阅默认Agent
INSERT INTO user_agent_subscriptions (id, user_id, agent_id, is_active, subscribed_at, expires_at)
SELECT 
    CONCAT('sub-', u.id, '-', a.id) as id,
    u.id as user_id,
    a.id as agent_id,
    TRUE as is_active,
    NOW() as subscribed_at,
    NULL as expires_at  -- 默认Agent永久免费
FROM users u
CROSS JOIN agents a
WHERE a.is_default = TRUE;

-- 10. 删除旧的domain_id列（在确认数据迁移正确后）
-- ALTER TABLE execution_plans DROP COLUMN domain_id;

-- 11. 删除domains表（在确认数据迁移正确后）
-- DROP TABLE domains;

-- 12. 插入示例执行计划
INSERT INTO execution_plans (id, name, description, agent_id, is_active, priority_order) VALUES
-- 通用助手的执行计划
('plan-general-chat', '通用对话', '处理日常对话和一般性问题', 'agent-general', TRUE, 1),

-- 日程助理的执行计划
('plan-schedule-create', '创建日程', '帮助用户创建和安排日程', 'agent-schedule', TRUE, 1),
('plan-schedule-manage', '管理日程', '管理和调整现有日程安排', 'agent-schedule', TRUE, 2),
('plan-schedule-remind', '日程提醒', '设置和管理日程提醒', 'agent-schedule', TRUE, 3),

-- 美工的执行计划
('plan-design-create', '创意设计', '进行创意设计和视觉创作', 'agent-designer', TRUE, 1),
('plan-design-review', '设计审查', '审查和优化设计作品', 'agent-designer', TRUE, 2),
('plan-design-guide', '设计指导', '提供设计建议和指导', 'agent-designer', TRUE, 3),

-- 活动策划的执行计划
('plan-event-planning', '活动策划', '制定完整的活动策划方案', 'agent-event-planner', TRUE, 1),
('plan-event-budget', '预算管理', '制定和管理活动预算', 'agent-event-planner', TRUE, 2),
('plan-event-execution', '活动执行', '指导活动的具体执行', 'agent-event-planner', TRUE, 3),

-- 商情搜集的执行计划
('plan-market-research', '市场调研', '进行深度市场调研分析', 'agent-market-researcher', TRUE, 1),
('plan-competitor-analysis', '竞品分析', '分析竞争对手和市场态势', 'agent-market-researcher', TRUE, 2),
('plan-trend-analysis', '趋势分析', '分析行业趋势和发展方向', 'agent-market-researcher', TRUE, 3);

-- 13. 为新的执行计划插入步骤
INSERT INTO execution_plan_steps (id, execution_plan_id, name, description, step_order, expected_duration_seconds, is_parallel) VALUES
-- 日程助理步骤
('step-schedule-understand', 'plan-schedule-create', '理解需求', '分析用户的日程安排需求', 1, 5, FALSE),
('step-schedule-plan', 'plan-schedule-create', '制定计划', '制定详细的日程安排计划', 2, 10, FALSE),
('step-schedule-confirm', 'plan-schedule-create', '确认安排', '确认最终的日程安排', 3, 5, FALSE),

-- 美工步骤
('step-design-brief', 'plan-design-create', '需求分析', '分析设计需求和目标', 1, 10, FALSE),
('step-design-concept', 'plan-design-create', '概念设计', '创建设计概念和方案', 2, 20, FALSE),
('step-design-refine', 'plan-design-create', '细化设计', '完善和细化设计作品', 3, 15, FALSE),

-- 活动策划步骤
('step-event-analysis', 'plan-event-planning', '需求分析', '分析活动目标和要求', 1, 15, FALSE),
('step-event-design', 'plan-event-planning', '方案设计', '设计活动方案和流程', 2, 25, FALSE),
('step-event-proposal', 'plan-event-planning', '方案提交', '提交完整的活动策划方案', 3, 10, FALSE);
