-- 创建领域表
CREATE TABLE domains (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    embedding_vector LONGTEXT, -- 存储向量的JSON格式
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    confidence_threshold DOUBLE NOT NULL DEFAULT 0.7,
    priority_order INT NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 为领域表创建索引
CREATE INDEX idx_domains_active_priority ON domains(is_active, priority_order);
CREATE INDEX idx_domains_name ON domains(name);

-- 修改执行计划表，添加领域外键
ALTER TABLE execution_plans 
ADD COLUMN domain_id VARCHAR(36),
ADD CONSTRAINT fk_execution_plans_domain 
    FOREIGN KEY (domain_id) REFERENCES domains(id) ON DELETE CASCADE;

-- 为执行计划表的领域外键创建索引
CREATE INDEX idx_execution_plans_domain ON execution_plans(domain_id);

-- 插入一些默认领域数据
INSERT INTO domains (id, name, description, is_active, confidence_threshold, priority_order) VALUES
('domain-general', '通用问题', '处理一般性问题和日常咨询', TRUE, 0.6, 100),
('domain-technical', '技术问题', '处理编程、软件开发、系统配置等技术相关问题', TRUE, 0.7, 10),
('domain-academic', '学术研究', '处理学术论文、研究方法、数据分析等学术相关问题', TRUE, 0.7, 20),
('domain-business', '商业咨询', '处理商业策略、市场分析、管理咨询等商业相关问题', TRUE, 0.7, 30),
('domain-creative', '内容创作', '处理文案写作、创意设计、内容策划等创作相关问题', TRUE, 0.7, 40),
('domain-translation', '翻译服务', '处理多语言翻译、本地化、语言学习等翻译相关问题', TRUE, 0.8, 50),
('domain-image', '图片处理', '处理图像编辑、视觉设计、图片分析等图像相关问题', TRUE, 0.8, 60);

-- 更新现有执行计划，分配到相应领域
UPDATE execution_plans SET domain_id = 'domain-general' WHERE domain = '通用问题' OR domain IS NULL;
UPDATE execution_plans SET domain_id = 'domain-technical' WHERE domain = '技术问题';
UPDATE execution_plans SET domain_id = 'domain-academic' WHERE domain = '学术研究';
UPDATE execution_plans SET domain_id = 'domain-business' WHERE domain = '商业咨询';
UPDATE execution_plans SET domain_id = 'domain-creative' WHERE domain = '内容创作';
UPDATE execution_plans SET domain_id = 'domain-translation' WHERE domain = '翻译服务';
UPDATE execution_plans SET domain_id = 'domain-image' WHERE domain = '图片处理';

-- 如果还有未分配的执行计划，分配到通用领域
UPDATE execution_plans SET domain_id = 'domain-general' WHERE domain_id IS NULL;

-- 删除旧的domain字段（在确认数据迁移正确后）
-- ALTER TABLE execution_plans DROP COLUMN domain;

-- 插入一些示例执行计划
INSERT INTO execution_plans (id, name, description, domain_id, is_active, priority_order) VALUES
('plan-general-chat', '通用对话', '处理日常对话和一般性问题', 'domain-general', TRUE, 1),
('plan-technical-debug', '技术调试', '帮助分析和解决技术问题', 'domain-technical', TRUE, 1),
('plan-technical-code-review', '代码审查', '审查代码质量和提供改进建议', 'domain-technical', TRUE, 2),
('plan-academic-research', '学术研究', '协助进行学术研究和论文写作', 'domain-academic', TRUE, 1),
('plan-business-analysis', '商业分析', '进行市场分析和商业策略制定', 'domain-business', TRUE, 1),
('plan-creative-writing', '创意写作', '协助进行文案创作和内容策划', 'domain-creative', TRUE, 1),
('plan-translation-multilang', '多语言翻译', '提供高质量的多语言翻译服务', 'domain-translation', TRUE, 1),
('plan-image-analysis', '图像分析', '分析图像内容和提供处理建议', 'domain-image', TRUE, 1);

-- 为新的执行计划插入步骤
INSERT INTO execution_plan_steps (id, execution_plan_id, name, description, step_order, expected_duration_seconds, is_parallel) VALUES
-- 通用对话步骤
('step-general-understand', 'plan-general-chat', '理解问题', '分析用户问题的意图和需求', 1, 5, FALSE),
('step-general-respond', 'plan-general-chat', '生成回复', '基于理解生成合适的回复', 2, 10, FALSE),

-- 技术调试步骤
('step-tech-analyze', 'plan-technical-debug', '问题分析', '分析技术问题的根本原因', 1, 10, FALSE),
('step-tech-solution', 'plan-technical-debug', '解决方案', '提供具体的解决方案和步骤', 2, 15, FALSE),
('step-tech-verify', 'plan-technical-debug', '方案验证', '验证解决方案的可行性', 3, 5, FALSE),

-- 学术研究步骤
('step-academic-topic', 'plan-academic-research', '主题分析', '分析研究主题和范围', 1, 10, FALSE),
('step-academic-method', 'plan-academic-research', '方法设计', '设计研究方法和流程', 2, 15, FALSE),
('step-academic-output', 'plan-academic-research', '成果输出', '生成研究报告或论文大纲', 3, 20, FALSE);
