# 领域驱动的AI助手架构

## 概述

本文档描述了AI助手系统的新架构，该架构以Domain（领域）作为聚合根，通过向量匹配技术实现智能的领域识别和执行计划选择。

## 核心概念

### 1. Domain（领域聚合根）
- **定义**: 代表一个问题领域，如技术问题、学术研究、商业咨询等
- **职责**: 
  - 包含该领域的所有执行计划
  - 维护领域的嵌入向量用于匹配
  - 设置领域匹配的置信度阈值
- **属性**:
  - `name`: 领域名称
  - `embeddingVector`: 领域的嵌入向量
  - `confidenceThreshold`: 匹配置信度阈值
  - `executionPlans`: 该领域下的所有执行计划

### 2. ExecutionPlan（执行计划）
- **定义**: 属于某个领域的具体执行方案
- **关系**: 多个执行计划属于一个领域
- **属性**:
  - `domain`: 所属领域
  - `embeddingVector`: 执行计划的嵌入向量
  - `steps`: 执行步骤

### 3. DomainService（领域服务）
- **职责**: 
  - 在内存中维护领域和执行计划的向量缓存
  - 执行向量相似度计算
  - 提供领域匹配和执行计划匹配功能

## 工作流程

### 1. 领域识别阶段
```
用户消息 → 生成查询向量 → 内存向量匹配 → 计算置信度 → 判断是否确定领域
```

#### 1.1 高置信度（领域确定）
- 置信度 >= 领域阈值
- 状态: `DOMAIN_CONFIRMED`
- 下一步: 进入执行计划选择阶段

#### 1.2 低置信度（领域不确定）
- 置信度 < 领域阈值
- 状态: `DOMAIN_UNCERTAIN`
- 行为: 与用户对话获取更多信息
- 回显: 显示可能的领域供用户确认

### 2. 执行计划选择阶段
```
确定的领域 → 匹配该领域下的执行计划 → 计算置信度 → 推荐或让用户选择
```

#### 2.1 有明确推荐
- 置信度高，有明确的最佳执行计划
- 行为: 回显推荐的执行计划，等待用户确认

#### 2.2 需要用户选择
- 置信度低或有多个相似的执行计划
- 行为: 回显所有可用的执行计划，让用户选择

### 3. 执行阶段
```
用户确认执行计划 → 创建执行上下文 → 按步骤执行 → 生成最终回复
```

## 技术实现

### 1. 向量匹配
- **存储**: 向量存储在数据库中，启动时加载到内存
- **算法**: 使用余弦相似度计算向量相似性
- **缓存**: 内存中维护 `ConcurrentHashMap` 缓存向量数据

### 2. 置信度判断
- **领域级别**: 每个领域有独立的置信度阈值
- **默认阈值**: 0.7（可配置）
- **动态调整**: 可根据历史匹配效果调整阈值

### 3. 状态管理
```kotlin
enum class DomainAdaptationStatus {
    PENDING,           // 待处理
    IN_PROGRESS,       // 进行中
    DOMAIN_UNCERTAIN,  // 领域不确定
    DOMAIN_CONFIRMED,  // 领域已确认
    PLAN_SELECTION,    // 执行计划选择中
    PLAN_CONFIRMED,    // 执行计划已确认
    COMPLETED          // 完成
}
```

## 数据库设计

### 1. domains 表
```sql
CREATE TABLE domains (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    embedding_vector LONGTEXT,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    confidence_threshold DOUBLE NOT NULL DEFAULT 0.7,
    priority_order INT NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

### 2. execution_plans 表（修改）
```sql
ALTER TABLE execution_plans 
ADD COLUMN domain_id VARCHAR(36),
ADD CONSTRAINT fk_execution_plans_domain 
    FOREIGN KEY (domain_id) REFERENCES domains(id);
```

## API 接口

### 1. 领域适配事件
```typescript
interface DomainAdaptationEvent {
  messageId: string;
  status: DomainAdaptationStatus;
  result: DomainAdaptationResult;
}
```

### 2. 执行计划选择事件
```typescript
interface ExecutionPlanSelectionEvent {
  messageId: string;
  result: ExecutionPlanSelectionResult;
}
```

### 3. 用户确认事件
```typescript
interface UserConfirmationEvent {
  messageId: string;
  type: 'domain' | 'execution-plan';
  selectedId: string;
}
```

## 配置示例

### 1. 默认领域配置
```yaml
domains:
  - name: "技术问题"
    confidence_threshold: 0.7
    priority: 10
  - name: "学术研究"
    confidence_threshold: 0.7
    priority: 20
  - name: "通用问题"
    confidence_threshold: 0.6
    priority: 100
```

### 2. 向量模型配置
```yaml
embedding:
  model: "text-embedding-ada-002"
  dimension: 1536
  cache_size: 1000
```

## 监控和调试

### 1. 关键指标
- 领域匹配准确率
- 执行计划选择准确率
- 平均置信度分布
- 用户确认率

### 2. 日志记录
- 向量匹配过程
- 置信度计算结果
- 用户选择行为
- 执行计划完成情况

## 扩展性

### 1. 新增领域
1. 在数据库中添加新的领域记录
2. 生成领域的嵌入向量
3. 创建该领域下的执行计划
4. 重启服务或调用刷新接口

### 2. 优化向量匹配
- 支持更高维度的向量
- 实现更复杂的相似度算法
- 添加向量索引以提高查询性能

### 3. 智能学习
- 根据用户反馈调整置信度阈值
- 优化向量表示
- 实现在线学习机制
