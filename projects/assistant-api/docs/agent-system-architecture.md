# AI助手Agent系统架构

## 概述

本文档描述了从Domain重构为Agent的AI助手系统架构。新系统以Agent为核心，实现了基于订阅的AI助手服务，支持多Agent协作和智能匹配。

## 核心变更

### 1. 概念重构
- **Domain** → **Agent**: 从抽象的问题领域转变为具体的AI助手角色
- **DomainAdaptationService** → **AgentService**: 合并适配功能，增加订阅管理
- 新增订阅模式：用户需要按月雇佣Agent

### 2. 实体模型

#### Agent（聚合根）
```kotlin
data class Agent(
    val id: String?,
    val name: String,                    // Agent名称
    val description: String?,            // Agent描述
    val avatarUrl: String?,             // Agent头像URL
    val embeddingVector: FloatArray?,   // 嵌入向量
    val isActive: Boolean,              // 是否激活
    val confidenceThreshold: Double,    // 置信度阈值
    val priorityOrder: Int,             // 优先级
    val monthlyPrice: Int,              // 月订阅价格（分）
    val isDefault: Boolean,             // 是否为默认Agent（免费）
    val executionPlans: List<ExecutionPlan>
)
```

#### UserAgentSubscription（用户订阅）
```kotlin
data class UserAgentSubscription(
    val id: String?,
    val user: User,
    val agent: Agent,
    val isActive: Boolean,
    val subscribedAt: LocalDateTime,
    val expiresAt: LocalDateTime?       // null表示永久订阅
)
```

#### SessionAgent（会话Agent关联）
```kotlin
data class SessionAgent(
    val id: String?,
    val session: ChatSession,
    val agent: Agent,
    val isActive: Boolean,              // 在当前会话中是否激活
    val addedAt: LocalDateTime
)
```

## 默认Agent配置

### 1. 通用助手
- **价格**: 免费
- **描述**: 处理一般性问题和日常咨询
- **头像**: 🤖
- **特点**: 默认Agent，所有用户自动拥有

### 2. 日程助理
- **价格**: 10元/月
- **描述**: 专业的日程管理和时间规划助手
- **头像**: 📅
- **功能**: 创建日程、管理日程、日程提醒

### 3. 美工
- **价格**: 200元/月
- **描述**: 专业的视觉设计师，提供创意设计和美术指导
- **头像**: 🎨
- **功能**: 创意设计、设计审查、设计指导

### 4. 活动策划
- **价格**: 200元/月
- **描述**: 经验丰富的活动策划专家
- **头像**: 🎉
- **功能**: 活动策划、预算管理、活动执行

### 5. 商情搜集
- **价格**: 1000元/月
- **描述**: 专业的市场研究分析师
- **头像**: 📊
- **功能**: 市场调研、竞品分析、趋势分析

## 工作流程

### 1. 新建会话流程
```
用户点击新建会话 → 弹出Agent选择对话框 → 用户选择Agent → 创建会话并关联Agent
```

### 2. 智能Agent匹配流程
```
用户发送消息 → 分析消息内容 → 匹配相关Agent → 激活匹配的Agent → 多Agent协作回复
```

### 3. Agent管理流程
```
会话顶部显示当前Agent → 用户可点击管理 → 添加/移除Agent → 切换激活状态
```

## 前端组件

### 1. AgentSelectionDialogComponent
- **功能**: Agent选择对话框
- **特性**: 
  - 显示所有可用Agent及其价格
  - 支持订阅未拥有的Agent
  - 支持单选或多选模式
  - 显示订阅状态

### 2. SessionAgentsBarComponent
- **功能**: 会话顶部Agent栏
- **特性**:
  - 显示当前会话的Agent头像
  - 支持点击管理Agent
  - 支持暂停/激活Agent
  - 显示协作状态

### 3. AgentService
- **功能**: Agent相关服务
- **特性**:
  - 管理用户订阅
  - 管理会话Agent
  - 格式化价格显示
  - 处理头像显示

## API接口

### 1. Agent管理
```
GET    /api/agents                     # 获取所有Agent
GET    /api/agents/{id}                # 获取特定Agent
POST   /api/agents/{id}/subscribe      # 订阅Agent
POST   /api/agents/{id}/unsubscribe    # 取消订阅
GET    /api/agents/subscriptions       # 获取用户订阅
```

### 2. 会话Agent管理
```
POST   /api/agents/sessions/{sessionId}/agents/{agentId}  # 添加Agent到会话
DELETE /api/agents/sessions/{sessionId}/agents/{agentId}  # 从会话移除Agent
GET    /api/agents/sessions/{sessionId}/agents           # 获取会话Agent
```

## 数据库设计

### 1. agents表
```sql
CREATE TABLE agents (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    avatar_url VARCHAR(500),
    embedding_vector LONGTEXT,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    confidence_threshold DOUBLE NOT NULL DEFAULT 0.7,
    priority_order INT NOT NULL DEFAULT 0,
    monthly_price INT NOT NULL DEFAULT 0,
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

### 2. user_agent_subscriptions表
```sql
CREATE TABLE user_agent_subscriptions (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    agent_id VARCHAR(36) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    subscribed_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

### 3. session_agents表
```sql
CREATE TABLE session_agents (
    id VARCHAR(36) PRIMARY KEY,
    session_id VARCHAR(36) NOT NULL,
    agent_id VARCHAR(36) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    added_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

## 智能匹配机制

### 1. Agent匹配
- 基于用户消息内容进行向量相似度计算
- 只在用户已订阅的Agent中进行匹配
- 支持多个Agent同时激活

### 2. 协作模式
- **单Agent模式**: 只有一个Agent时，该Agent处理所有消息
- **多Agent模式**: 多个Agent协作，根据消息内容智能分配

### 3. 激活管理
- 用户可以手动激活/暂停Agent
- 系统可以根据消息内容自动激活相关Agent
- 至少保持一个Agent激活状态

## 订阅管理

### 1. 订阅模式
- **免费Agent**: 默认Agent，所有用户自动拥有
- **付费Agent**: 需要按月订阅，支持到期自动续费

### 2. 订阅状态
- `NOT_SUBSCRIBED`: 未订阅
- `SUBSCRIBED`: 已订阅
- `EXPIRED`: 已过期

### 3. 权限控制
- 只能使用已订阅的Agent
- 过期Agent自动从会话中移除
- 支持订阅状态实时更新

## 用户体验

### 1. 新用户引导
- 新建会话时强制选择Agent
- 提供默认免费Agent
- 引导用户了解付费Agent功能

### 2. 可视化设计
- Agent头像清晰区分不同角色
- 价格信息明确显示
- 订阅状态一目了然

### 3. 交互优化
- 一键订阅/取消订阅
- 拖拽式Agent管理
- 实时状态更新

## 扩展性

### 1. 新Agent添加
- 数据库添加Agent记录
- 设计专属头像
- 配置执行计划
- 生成嵌入向量

### 2. 定价策略
- 支持灵活的定价模式
- 支持促销和折扣
- 支持企业版订阅

### 3. 功能增强
- Agent能力评级
- 用户评价系统
- Agent使用统计
- 智能推荐系统

这个新的Agent系统提供了更加直观和商业化的AI助手服务，通过订阅模式实现了可持续的商业模式，同时保持了强大的技术能力和用户体验。
